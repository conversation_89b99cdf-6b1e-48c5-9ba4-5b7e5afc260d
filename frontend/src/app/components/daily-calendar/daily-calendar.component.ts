import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ReservationService } from '../../services/reservation.service';
import { Reservation } from '../../models/reservation.model';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SpaceService } from '../../services/space.service';
import { Space, SpaceStatus } from '../../models/space.model';
import { of } from 'rxjs';

interface TimeSlot {
  hour: string;
  time24: string;
  reservations?: Reservation[];
}

interface SpaceAvailability {
  space: Space;
  reservations: Reservation[];
}

@Component({
  selector: 'app-daily-calendar',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzIconModule,
    NzTagModule,
    NzToolTipModule,
    NzButtonModule
  ],
  templateUrl: './daily-calendar.component.html',
  styleUrls: ['./daily-calendar.component.css']
})
export class DailyCalendarComponent implements OnInit, OnDestroy {
  timeSlots: TimeSlot[] = [];
  spacesAvailability: SpaceAvailability[] = [];
  currentDate = new Date();
  loading = true;

  constructor(
    private spaceService: SpaceService,
    private router: Router,
    private message: NzMessageService,
    private reservationService: ReservationService
  ) {}

  ngOnInit() {
    this.generateTimeSlots();
    this.loadSpacesAndReservations();
  }

  ngOnDestroy() {
    this.clearLongPressTimer();
  }

  generateTimeSlots() {
    this.timeSlots = [];
    for (let hour = 9; hour <= 18; hour++) {
      // Ajouter l'heure pleine
      const time24 = `${hour.toString().padStart(2, '0')}:00`;
      const time12 = hour <= 12 ? `${hour}:00 AM` : `${hour - 12}:00 PM`;
      if (hour === 12) {
        this.timeSlots.push({
          hour: '12:00 PM',
          time24: time24
        });
      } else {
        this.timeSlots.push({
          hour: time12,
          time24: time24
        });
      }

      // Ajouter la demi-heure (sauf pour la dernière heure)
      if (hour < 18) {
        const time24Half = `${hour.toString().padStart(2, '0')}:30`;
        const time12Half = hour < 12 ? `${hour}:30 AM` : hour === 12 ? '12:30 PM' : `${hour - 12}:30 PM`;
        this.timeSlots.push({
          hour: time12Half,
          time24: time24Half
        });
      }
    }
  }

  loadSpacesAndReservations() {
    this.loading = true;

    // Charger les espaces actifs et les réservations en parallèle
    Promise.all([
      this.spaceService.getActiveSpaces().toPromise(),
      this.loadTodayReservations()
    ]).then(([spaces, reservations]) => {
      // Créer la structure de disponibilité avec tous les espaces actifs
      this.spacesAvailability = (spaces || []).map(space => ({
        space: space,
        reservations: (reservations || []).filter((res: Reservation) => res.spaceId === space.id)
      }));

      this.loading = false;
    }).catch(error => {
      console.error('Erreur lors du chargement des données:', error);
      this.loading = false;
    });
  }

  private loadTodayReservations(): Promise<Reservation[]> {
    // Créer les dates de début et fin pour le jour actuel
    const startOfDay = new Date(this.currentDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(this.currentDate);
    endOfDay.setHours(23, 59, 59, 999);

    const filters = {
      dateRange: {
        start: startOfDay,
        end: endOfDay
      }
    };

    return this.reservationService.getReservations(filters).toPromise().then(result => result || []);
  }



  private isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  isTimeSlotReserved(space: Space, timeSlot: TimeSlot): Reservation | null {
    const spaceAvailability = this.spacesAvailability.find(sa => sa.space.id === space.id);
    if (!spaceAvailability) return null;

    const [slotHour, slotMinute] = timeSlot.time24.split(':').map(Number);
    const slotTimeInMinutes = slotHour * 60 + slotMinute;

    return spaceAvailability.reservations.find(reservation => {
      // Les dates sont déjà des objets Date
      const startTime = reservation.startTime;
      const endTime = reservation.endTime;

      // Utiliser getHours() et getMinutes() qui donnent l'heure locale
      const startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();
      const endTimeInMinutes = endTime.getHours() * 60 + endTime.getMinutes();

      return slotTimeInMinutes >= startTimeInMinutes && slotTimeInMinutes < endTimeInMinutes;
    }) || null;
  }

  isFirstSlotOfReservation(space: Space, timeSlot: TimeSlot, reservation: Reservation): boolean {
    // Les dates sont déjà des objets Date
    const startTime = reservation.startTime;
    const [slotHour, slotMinute] = timeSlot.time24.split(':').map(Number);

    const calendarStartMinutes = 9 * 60; // 9h00 = 540 minutes
    const startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    const slotTimeInMinutes = slotHour * 60 + slotMinute;

    // Si la réservation commence avant 9h00 et que c'est le premier slot visible (9h00)
    if (startTimeInMinutes < calendarStartMinutes && slotTimeInMinutes === calendarStartMinutes) {
      return true;
    }

    // Sinon, vérifier si c'est exactement le slot de début de la réservation
    return startTime.getHours() === slotHour && startTime.getMinutes() === slotMinute;
  }

  getReservationDurationInSlots(reservation: Reservation): number {
    // Les dates sont déjà des objets Date
    const startTime = reservation.startTime;
    const endTime = reservation.endTime;

    // Limites de la plage horaire visible (9h00 à 18h30)
    const calendarStartMinutes = 9 * 60; // 9h00 = 540 minutes
    const calendarEndMinutes = 18 * 60 + 30; // 18h30 = 1110 minutes

    let startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    let endTimeInMinutes = endTime.getHours() * 60 + endTime.getMinutes();

    // Clipper les heures aux limites du calendrier visible
    startTimeInMinutes = Math.max(startTimeInMinutes, calendarStartMinutes);
    endTimeInMinutes = Math.min(endTimeInMinutes, calendarEndMinutes);

    // Si la réservation est complètement hors de la plage visible, retourner 0
    if (startTimeInMinutes >= endTimeInMinutes) {
      return 0;
    }

    return Math.ceil((endTimeInMinutes - startTimeInMinutes) / 30); // 30 minutes par slot
  }

  getReservationTopOffset(reservation: Reservation): number {
    const startTime = reservation.startTime;
    const calendarStartMinutes = 9 * 60; // 9h00 = 540 minutes

    let startTimeInMinutes = startTime.getHours() * 60 + startTime.getMinutes();

    // Si la réservation commence avant 9h00, la clipper à 9h00
    if (startTimeInMinutes < calendarStartMinutes) {
      return 0; // Pas d'offset, commence au début du calendrier
    }

    // Calculer l'offset en pixels depuis le début du créneau
    const startMinutes = startTime.getMinutes();
    const slotHeight = this.getSlotHeight();

    // Si la réservation commence à 15 minutes dans le créneau, offset de 50%
    // Si elle commence à 30 minutes, c'est le créneau suivant
    const offsetPercentage = (startMinutes % 30) / 30;
    return offsetPercentage * slotHeight;
  }

  getSlotHeight(): number {
    // Détection mobile basique
    return window.innerWidth <= 768 ? 60 : 40;
  }

  getReservationHeight(reservation: Reservation): number {
    const slots = this.getReservationDurationInSlots(reservation);
    const slotHeight = this.getSlotHeight();
    return slots * slotHeight;
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    // Force la re-détection des hauteurs lors du redimensionnement
    // Angular va automatiquement recalculer les styles
  }

  @HostListener('document:mouseup', ['$event'])
  @HostListener('document:touchend', ['$event'])
  onDocumentMouseUp(event: any) {
    if (this.isDragging) {
      this.resetDragState();
    }
  }

  getReservationStatus(reservation: Reservation): string {
    switch (reservation.status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'default';
      default:
        return 'default';
    }
  }

  getReservationColor(reservation: Reservation): string {
    switch (reservation.status) {
      case 'confirmed':
        return '#6E56CF'; // Violet principal pour confirmé
      case 'pending':
        return '#B8A9E8'; // Violet soft pour en attente
      case 'cancelled':
        return '#C7C7CC'; // Gris plus clair pour annulé
      default:
        return '#d9d9d9';
    }
  }

  getSpaceIcon(spaceType: string): string {
    switch (spaceType) {
      case 'meeting_room':
        return 'team';
      case 'office':
        return 'home';
      case 'desk':
        return 'desktop';
      case 'phone_booth':
        return 'phone';
      default:
        return 'appstore';
    }
  }

  getSpaceCapacityIcon(capacity: number): string {
    if (capacity <= 2) return 'user';
    if (capacity <= 6) return 'team';
    return 'usergroup-add';
  }

  formatReservationTime(reservation: Reservation): string {
    // Les dates sont déjà des objets Date
    const start = reservation.startTime;
    const end = reservation.endTime;

    // Utiliser toLocaleTimeString avec la timezone française
    const formatTime = (date: Date) => {
      return date.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    };

    return `${formatTime(start)} - ${formatTime(end)}`;
  }

  getReservationTooltip(reservation: Reservation): string {
    return `${reservation.userName}\n${this.formatReservationTime(reservation)}\nStatut: ${this.getStatusLabel(reservation.status)}`;
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'Confirmé';
      case 'pending':
        return 'En attente';
      case 'cancelled':
        return 'Annulé';
      default:
        return 'Inconnu';
    }
  }

  refreshCalendar() {
    this.loadSpacesAndReservations();
  }

  // Méthodes de navigation par jour
  goToPreviousDay() {
    const previousDay = new Date(this.currentDate);
    previousDay.setDate(previousDay.getDate() - 1);
    this.navigateToDate(previousDay);
  }

  goToNextDay() {
    const nextDay = new Date(this.currentDate);
    nextDay.setDate(nextDay.getDate() + 1);
    this.navigateToDate(nextDay);
  }

  goToToday() {
    this.navigateToDate(new Date());
  }

  // Méthode de navigation optimisée qui évite le scroll
  private navigateToDate(newDate: Date) {
    this.currentDate = newDate;
    this.loadDayReservationsQuietly();
  }

  // Méthode optimisée pour charger seulement les réservations du jour
  private async loadDayReservations() {
    this.loading = true;
    try {
      const reservations = await this.loadTodayReservations();
      this.updateCalendarWithReservations(reservations);
    } catch (error) {
      console.error('Erreur lors du chargement des réservations:', error);
      this.message.error('Erreur lors du chargement des réservations');
    } finally {
      this.loading = false;
    }
  }

  // Méthode silencieuse pour la navigation qui évite le scroll
  private async loadDayReservationsQuietly() {
    this.loading = true;
    try {
      // Charger directement les réservations sans passer par loadTodayReservations
      const startDate = new Date(this.currentDate);
      startDate.setHours(0, 0, 0, 0);

      const endDate = new Date(this.currentDate);
      endDate.setHours(23, 59, 59, 999);

      const filters = {
        dateRange: {
          start: startDate,
          end: endDate
        }
      };

      const reservations = await this.reservationService.getReservations(filters).toPromise();
      this.updateCalendarWithReservations(reservations || []);
    } catch (error) {
      console.error('Erreur lors du chargement des réservations:', error);
    } finally {
      this.loading = false;
    }
  }

  isToday(): boolean {
    const today = new Date();
    return this.isSameDay(this.currentDate, today);
  }

  // Méthode pour mettre à jour le calendrier avec les nouvelles réservations
  private updateCalendarWithReservations(reservations: Reservation[]) {
    // Réinitialiser les créneaux
    this.timeSlots.forEach(slot => {
      slot.reservations = [];
    });

    // Réassigner les réservations aux créneaux
    reservations.forEach(reservation => {
      const startTime = new Date(reservation.startTime);
      const endTime = new Date(reservation.endTime);

      this.timeSlots.forEach(slot => {
        const slotStart = new Date(this.currentDate);
        const [hours, minutes] = slot.time24.split(':').map(Number);
        slotStart.setHours(hours, minutes, 0, 0);

        const slotEnd = new Date(slotStart);
        slotEnd.setMinutes(slotEnd.getMinutes() + 30);

        // Vérifier si la réservation chevauche avec ce créneau
        if (startTime < slotEnd && endTime > slotStart) {
          if (!slot.reservations) {
            slot.reservations = [];
          }
          slot.reservations.push(reservation);
        }
      });
    });

    // Mettre à jour la disponibilité des espaces
    this.updateSpacesAvailability(reservations);
  }

  // Méthode pour mettre à jour la disponibilité des espaces
  private updateSpacesAvailability(reservations: Reservation[]) {
    this.spacesAvailability.forEach(spaceAvail => {
      // Filtrer les réservations pour cet espace
      spaceAvail.reservations = reservations.filter(res => res.spaceId === spaceAvail.space.id);
    });
  }

  // Variables pour le glisser-déposer
  private isDragging = false;
  private dragStartSpace: Space | null = null;
  private dragStartTimeSlot: TimeSlot | null = null;
  private dragEndTimeSlot: TimeSlot | null = null;
  selectedSlots: Set<string> = new Set(); // Pour tracker les slots sélectionnés

  // Variables pour le long press sur mobile
  private longPressTimer: any = null;
  private isLongPressActive = false;
  private longPressDelay = 500; // 500ms pour activer le mode sélection
  private touchStartPosition = { x: 0, y: 0 };
  private touchMoveThreshold = 10; // pixels

  // Méthode pour cliquer sur une réservation
  onReservationClick(reservation: Reservation): void {
    this.router.navigate(['/reservations', reservation.id]);
  }

  // Méthodes pour le glisser-déposer
  onSlotMouseDown(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    // Vérifier si le jour est dans le passé
    if (this.isTimeSlotInPast(timeSlot)) {
      this.message.warning('Impossible de réserver pour un jour passé');
      return;
    }

    event.preventDefault();
    this.isDragging = true;
    this.dragStartSpace = space;
    this.dragStartTimeSlot = timeSlot;
    this.dragEndTimeSlot = timeSlot;
    this.updateSelectedSlots();
  }

  onSlotMouseOver(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    if (this.isDragging && this.dragStartSpace?.id === space.id) {
      this.dragEndTimeSlot = timeSlot;
      this.updateSelectedSlots();
    }
  }

  onSlotMouseUp(space: Space, timeSlot: TimeSlot, event: MouseEvent): void {
    if (this.isDragging && this.dragStartSpace?.id === space.id) {
      this.isDragging = false;

      if (this.dragStartTimeSlot && this.dragEndTimeSlot) {
        const startTime = this.getTimeSlotDateTime(this.dragStartTimeSlot);
        const endTime = this.getTimeSlotDateTime(this.dragEndTimeSlot);

        // S'assurer que l'heure de fin est après l'heure de début
        const actualStartTime = startTime < endTime ? startTime : endTime;
        const actualEndTime = startTime < endTime ? endTime : startTime;

        // Ajouter 30 minutes à l'heure de fin pour avoir la durée complète
        actualEndTime.setMinutes(actualEndTime.getMinutes() + 30);

        // Vérifier si la réservation est pour un jour passé (pas l'heure)
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const startOfReservationDay = new Date(actualStartTime.getFullYear(), actualStartTime.getMonth(), actualStartTime.getDate());

        if (startOfReservationDay < startOfToday) {
          this.message.error('Impossible de créer une réservation pour un jour passé.');
          this.resetDragState();
          return;
        }

        // Vérifier s'il y a des créneaux occupés dans la sélection
        if (this.hasConflictingReservations(space, actualStartTime, actualEndTime)) {
          // Afficher un message d'erreur
          this.message.error('Impossible de créer une réservation qui traverse un créneau déjà occupé.');
          this.resetDragState();
          return;
        }

        // Naviguer vers le formulaire de réservation avec les données pré-remplies
        this.router.navigate(['/reservation-form'], {
          queryParams: {
            spaceId: space.id,
            startTime: actualStartTime.toISOString(),
            endTime: actualEndTime.toISOString()
          }
        });
      }

      // Reset des variables
      this.dragStartSpace = null;
      this.dragStartTimeSlot = null;
      this.dragEndTimeSlot = null;
      this.selectedSlots.clear();
    }
  }

  // Méthodes pour le support tactile (mobile)
  onSlotTouchStart(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    // Vérifier si le créneau est dans le passé
    if (this.isTimeSlotInPast(timeSlot)) {
      this.message.warning('Impossible de réserver un créneau dans le passé');
      return;
    }

    // Ne pas empêcher le comportement par défaut immédiatement pour permettre le scroll
    const touch = event.touches[0];
    this.touchStartPosition = { x: touch.clientX, y: touch.clientY };
    this.isLongPressActive = false;

    // Démarrer le timer pour le long press
    this.longPressTimer = setTimeout(() => {
      // Long press détecté - activer le mode sélection
      this.isLongPressActive = true;
      this.isDragging = true;
      this.dragStartSpace = space;
      this.dragStartTimeSlot = timeSlot;
      this.dragEndTimeSlot = timeSlot;
      this.updateSelectedSlots();

      // Feedback haptique si disponible
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, this.longPressDelay);
  }

  onSlotTouchMove(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    const touch = event.touches[0];

    // Calculer la distance de mouvement depuis le début
    const deltaX = Math.abs(touch.clientX - this.touchStartPosition.x);
    const deltaY = Math.abs(touch.clientY - this.touchStartPosition.y);
    const totalMovement = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Si le mouvement total dépasse le seuil avant le long press, annuler le timer
    // Cela permet le scroll libre dans toutes les directions
    if (totalMovement > this.touchMoveThreshold && !this.isLongPressActive) {
      this.clearLongPressTimer();
      return; // Permettre le scroll libre
    }

    // Si le mode sélection est actif, gérer le glissement
    if (this.isDragging && this.isLongPressActive && this.dragStartSpace?.id === space.id) {
      // Empêcher le scroll seulement quand on est en mode sélection
      event.preventDefault();

      // Obtenir les coordonnées du toucher
      if (touch) {
        // Trouver l'élément sous le doigt
        const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
        if (elementBelow) {
          // Trouver le slot correspondant
          const slotElement = elementBelow.closest('.time-cell');
          if (slotElement) {
            // Trouver l'index du slot dans la grille
            const timeCells = Array.from(slotElement.parentElement?.children || []);
            const slotIndex = timeCells.indexOf(slotElement);

            if (slotIndex >= 0 && slotIndex < this.timeSlots.length) {
              this.dragEndTimeSlot = this.timeSlots[slotIndex];
              this.updateSelectedSlots();
            }
          }
        }
      }
    }
  }

  onSlotTouchEnd(space: Space, timeSlot: TimeSlot, event: TouchEvent): void {
    // Nettoyer le timer du long press
    this.clearLongPressTimer();

    // Si le mode sélection était actif, traiter la réservation
    if (this.isDragging && this.isLongPressActive && this.dragStartSpace?.id === space.id) {
      event.preventDefault();
      this.isDragging = false;
      this.isLongPressActive = false;

      if (this.dragStartTimeSlot && this.dragEndTimeSlot) {
        const startTime = this.getTimeSlotDateTime(this.dragStartTimeSlot);
        const endTime = this.getTimeSlotDateTime(this.dragEndTimeSlot);

        // S'assurer que l'heure de fin est après l'heure de début
        const actualStartTime = startTime < endTime ? startTime : endTime;
        const actualEndTime = startTime < endTime ? endTime : startTime;

        // Ajouter 30 minutes à l'heure de fin pour avoir la durée complète
        actualEndTime.setMinutes(actualEndTime.getMinutes() + 30);

        // Vérifier si la réservation est pour un jour passé (pas l'heure)
        const now = new Date();
        const startOfToday = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const startOfReservationDay = new Date(actualStartTime.getFullYear(), actualStartTime.getMonth(), actualStartTime.getDate());

        if (startOfReservationDay < startOfToday) {
          this.message.error('Impossible de créer une réservation pour un jour passé.');
          this.resetDragState();
          return;
        }

        // Vérifier s'il y a des créneaux occupés dans la sélection
        if (this.hasConflictingReservations(space, actualStartTime, actualEndTime)) {
          // Afficher un message d'erreur
          this.message.error('Impossible de créer une réservation qui traverse un créneau déjà occupé.');
          this.resetDragState();
          return;
        }

        // Naviguer vers le formulaire de réservation avec les données pré-remplies
        this.router.navigate(['/reservation-form'], {
          queryParams: {
            spaceId: space.id,
            startTime: actualStartTime.toISOString(),
            endTime: actualEndTime.toISOString()
          }
        });
      }

      // Reset des variables
      this.dragStartSpace = null;
      this.dragStartTimeSlot = null;
      this.dragEndTimeSlot = null;
      this.selectedSlots.clear();
    } else {
      // Si ce n'était pas un long press, juste nettoyer les variables
      this.resetDragState();
    }
  }

  private getTimeSlotDateTime(timeSlot: TimeSlot): Date {
    // Utiliser la date du calendrier (currentDate) au lieu d'aujourd'hui
    const [hours, minutes] = timeSlot.time24.split(':').map(Number);

    // Créer une date en heure locale pour éviter les problèmes de timezone
    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate(), hours, minutes);
  }

  // Vérifier si un créneau est dans le passé - maintenant seulement pour les jours passés
  private isTimeSlotInPast(timeSlot: TimeSlot): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Début de la journée actuelle

    const slotDate = new Date(this.currentDate);
    slotDate.setHours(0, 0, 0, 0); // Début du jour du créneau

    // Désactiver seulement si le jour entier est passé, pas les heures
    return slotDate < today;
  }

  // Méthode publique pour le template
  isSlotInPast(timeSlot: TimeSlot): boolean {
    return this.isTimeSlotInPast(timeSlot);
  }

  // Méthodes pour la sélection visuelle
  updateSelectedSlots(): void {
    this.selectedSlots.clear();

    if (!this.dragStartSpace || !this.dragStartTimeSlot || !this.dragEndTimeSlot) {
      return;
    }

    const startIndex = this.timeSlots.findIndex(slot => slot.time24 === this.dragStartTimeSlot!.time24);
    const endIndex = this.timeSlots.findIndex(slot => slot.time24 === this.dragEndTimeSlot!.time24);

    const minIndex = Math.min(startIndex, endIndex);
    const maxIndex = Math.max(startIndex, endIndex);

    for (let i = minIndex; i <= maxIndex; i++) {
      const slotKey = `${this.dragStartSpace.id}-${this.timeSlots[i].time24}`;
      this.selectedSlots.add(slotKey);
    }
  }

  isSlotSelected(space: Space, timeSlot: TimeSlot): boolean {
    const slotKey = `${space.id}-${timeSlot.time24}`;
    return this.selectedSlots.has(slotKey);
  }

  // Méthodes utilitaires pour le long press
  private clearLongPressTimer(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }

  private resetDragState(): void {
    this.isDragging = false;
    this.isLongPressActive = false;
    this.dragStartSpace = null;
    this.dragStartTimeSlot = null;
    this.dragEndTimeSlot = null;
    this.selectedSlots.clear();
    this.clearLongPressTimer();
  }

  // Vérifier s'il y a des conflits de réservation dans la plage sélectionnée
  private hasConflictingReservations(space: Space, startTime: Date, endTime: Date): boolean {
    const spaceAvailability = this.spacesAvailability.find(sa => sa.space.id === space.id);
    if (!spaceAvailability) return false;

    // Convertir les heures en minutes pour faciliter la comparaison
    const selectionStartMinutes = startTime.getHours() * 60 + startTime.getMinutes();
    const selectionEndMinutes = endTime.getHours() * 60 + endTime.getMinutes();

    // Vérifier chaque réservation existante
    for (const reservation of spaceAvailability.reservations) {
      const reservationStartMinutes = reservation.startTime.getHours() * 60 + reservation.startTime.getMinutes();
      const reservationEndMinutes = reservation.endTime.getHours() * 60 + reservation.endTime.getMinutes();

      // Vérifier s'il y a un chevauchement
      // Il y a conflit si la sélection commence avant la fin de la réservation
      // ET si la sélection se termine après le début de la réservation
      if (selectionStartMinutes < reservationEndMinutes && selectionEndMinutes > reservationStartMinutes) {
        return true;
      }
    }

    return false;
  }

  formatCurrentDate(): string {
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    };
    return this.currentDate.toLocaleDateString('fr-FR', options);
  }
}
